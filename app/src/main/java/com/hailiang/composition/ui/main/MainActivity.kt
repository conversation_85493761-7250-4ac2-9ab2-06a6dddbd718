package com.hailiang.composition.ui.main

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.View
import android.view.ViewStub
import android.widget.ImageView
import android.widget.TextView
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.hailiang.common.base.BaseActivity
import com.hailiang.common.event.FlowEventBus
import com.hailiang.common.event.receiver.TimeChangeReceiver
import com.hailiang.common.event.receiver.TimeTickEvent
import com.hailiang.common.ext.recycleriew.GridSpacingItemDecoration
import com.hailiang.common.util.Constants
import com.hailiang.composition.data.Repository
import com.hailiang.composition.data.bean.CreateWorkBean
import com.hailiang.composition.data.bean.TaskBean
import com.hailiang.composition.data.bean.TaskMaterialBean
import com.hailiang.composition.data.bean.WorkInfo
import com.hailiang.composition.dialog.CommonDialog
import com.hailiang.composition.ui.guidance.TakePhotoGuidanceActivity
import com.hailiang.composition.ui.photograph.TakePhotoCompositionActivity
import com.hailiang.composition.ui.practice.CompositionGuidanceActivity
import com.hailiang.core.widget.AvatarHelper
import com.hailiang.hlutil.HLog
import com.hailiang.hlutil.HTag
import com.hailiang.hlutil.dpInt
import com.hailiang.hlutil.id
import com.hailiang.opensdk.LauncherData
import com.hailiang.opensdk.consts.UserType
import com.hailiang.recyclerview.adapter.BaseRecyclerViewAdapter
import com.hailiang.recyclerview.adapter.listener.OnItemClickListener
import com.hailiang.textcorrection.dl.TextCorrectionManager
import com.hailiang.xxb.composition.R
import com.hailiang.xxb.refresh.SmartRefreshLayout
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.Locale
import androidx.core.view.isVisible

class MainActivity : BaseActivity() {
    private val ivAvatar: ImageView by id(R.id.iv_avatar)
    private val tvName: TextView by id(R.id.tv_name)
    private val tvTime: TextView by id(R.id.tv_time)
    private val vsEmpty: ViewStub by id(R.id.vs_empty)
    private val refreshLayout: SmartRefreshLayout by id(R.id.smart_refresh)
    private val recyclerView: RecyclerView by id(R.id.rv)
    private val btnAdd: View by id(R.id.ll_write)
    private val adapter = BaseRecyclerViewAdapter(CompositionViewPresenter())
    private val tvPhotoExample: TextView by id(R.id.tv_photo_example)
    private val timeChangeReceiver = TimeChangeReceiver()
    private val takePhotoGuideLauncher =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { _ ->
            btnAdd.callOnClick()
        }

    // 拍照页面启动器 - 需要刷新列表
    private val takePhotoLauncher =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            if (result.resultCode == RESULT_OK) {
                // 拍照成功，刷新列表
                refreshLayout.autoRefresh()
                result.data?.let { intent ->
                    val WORK_ID = "workId"
                    val WORK_STATE_ID = "workStateId"
                    val workId = intent.getLongExtra(WORK_ID, 0)
                    val workStateId = intent.getLongExtra(WORK_STATE_ID, 0)
                    CompositionGuidanceActivity.start(
                        this, workId, workStateId
                    )
                }
            }
        }

    // 查看详情页面启动器 - 不需要刷新列表
    private val viewDetailLauncher =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { _ ->
            // 查看详情返回，不刷新列表
        }

    // 查看示例页面启动器 - 不需要刷新列表
    private val viewExampleLauncher =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { _ ->
            // 查看示例返回，不刷新列表
        }

    private val viewModel by viewModels<MainViewModel>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        timeChangeReceiver.register(this)

        viewModel.getTextCorrectionInfo(this@MainActivity)
    }

    override fun onDestroy() {
        super.onDestroy()
        timeChangeReceiver.unregister(this)
    }

    override fun onResume() {
        super.onResume()
        // 只在列表为空时才自动刷新
        if (vsEmpty.isVisible && adapter.itemCount == 0){
            viewModel.getCompositionList(true)
        }
    }

    override fun initView() {
        setContentView(R.layout.activity_main)

        AvatarHelper.load(
            ivAvatar,
            LauncherData.getUserInfo()?.headUrl,
            UserType.STUDENT,
            LauncherData.getUserInfo()?.sex ?: 0
        )
        tvName.text = Constants.username

        recyclerView.layoutManager = GridLayoutManager(this, 3)
        recyclerView.addItemDecoration(GridSpacingItemDecoration(20.dpInt, 20.dpInt))
        recyclerView.adapter = adapter
        adapter.setOnItemClickListener(object : OnItemClickListener {
            override fun onItemClick(v: View, data: Any, position: Int) {
                val workInfo = data as WorkInfo
                CompositionGuidanceActivity.start(
                    this@MainActivity, workInfo.schoolworkId, workInfo.schoolworkStateId
                )
            }
        })
        adapter.addOnItemChildClickListener(R.id.iv_delete, object : OnItemClickListener {
            override fun onItemClick(v: View, data: Any, position: Int) {
                CommonDialog.Builder()
                    .setTitle("提示")
                    .setContentText("删除后无法恢复，确定删除吗？")
                    .setPositiveText("再想想")
                    .setPositiveAction {
                        val workInfo = data as WorkInfo
                        viewModel.deleteComposition(workInfo.schoolworkStateId) {
                            viewModel.getCurrentIndexCompositionList()
                            adapter.remove(data)
                            if (adapter.dataList.isEmpty()) {
                                refreshLayout.autoRefresh()
                            }
                        }
                    }.show(supportFragmentManager)

            }
        })
        btnAdd.setOnClickListener {
            viewModel.requestBeginnerGuidance(
                guideBlock = {
                    takePhotoGuideLauncher.launch(
                        Intent(this@MainActivity, TakePhotoGuidanceActivity::class.java)
                    )
                },
                photoBlock = {
                    // 使用takePhotoLauncher启动拍照页，返回时刷新列表
                    val intent = TakePhotoCompositionActivity.createIntent(this@MainActivity, 0, 0)
                    takePhotoLauncher.launch(intent)
                }
            )
        }
        tvPhotoExample.setOnClickListener {
            // 使用viewExampleLauncher启动示例页，返回时不刷新列表
            val intent = Intent(this@MainActivity, TakePhotoGuidanceActivity::class.java)
            viewExampleLauncher.launch(intent)
        }

        refreshLayout.setEnableAutoLoadMore(true)
        refreshLayout.setOnRefreshListener {
            refreshLayout.resetNoMoreData()
            viewModel.getCompositionList(true)
        }
        refreshLayout.setOnLoadMoreListener {
            viewModel.getCompositionList(false)
        }

        //fixme 测试
//        tvName.setOnClickListener {
////            startActivity(Intent(this@MainActivity, TestActivity::class.java))
//            val createWorkBean = CreateWorkBean().apply {
//                compositionImageList =
//                    listOf("https://jzjx-dev-resource.hailiangedu.com/workcloud/2025/05/24/CROP_120250524151931276.jpeg")
//                taskList = listOf(
//                    TaskBean(
//                        id = -1,
//                        submitType = "takePhoto",
//                        taskMaterialList = listOf(
//                            TaskMaterialBean(
//                                materialFileType = "picture",
//                                materialType = "task",
//                                materialFileUrl = "https://jzjx-dev-resource.hailiangedu.com/workcloud/2025/05/24/CROP_120250524151901958.jpeg"
//                            )
//                        )
//                    )
//                )
//            }
//            lifecycleScope.launch {
//                Repository().addComposition(createWorkBean)
//            }
//        }
    }

    override fun observeData() {
        FlowEventBus.observeEventT<TimeTickEvent>(this) {
            updateTime()
        }
        viewModel.compositionListLiveData.observe(this) {
            refreshLayout.finishRefresh(true)
            if ((it == null || it.list.isEmpty())) {
                if (adapter.itemCount == 0) {
                    vsEmpty.visibility = View.VISIBLE
                    refreshLayout.visibility = View.GONE
                }
                refreshLayout.finishLoadMoreWithNoMoreData()
                return@observe
            }
            refreshLayout.visibility = View.VISIBLE
            refreshLayout.finishLoadMore(0)
            vsEmpty.visibility = View.GONE

            // 检查是否为删除后的数据补充
            val isRefillAfterDelete = viewModel.isRefillAfterDeleteAndReset()

            if (it.currPage == 1) {
                adapter.clear()
            } else if (isRefillAfterDelete) {
                // 删除后补充数据，需要去重处理
                val existingIds = adapter.dataList.map { item ->
                    (item as WorkInfo).schoolworkStateId
                }.toSet()
                val newItems = it.list.filter { newItem ->
                    !existingIds.contains(newItem.schoolworkStateId)
                }
                adapter.addAll(newItems)
                return@observe
            }
            adapter.addAll(it.list)
        }
    }

    override fun initData() {
        updateTime()
        // 首次进入页面时加载数据
        if (adapter.itemCount == 0) {
            viewModel.getCompositionList(true)
        }
    }

    @SuppressLint("SetTextI18n")
    private fun updateTime() {
        val now = LocalDateTime.now()
        val timeFormatter = DateTimeFormatter.ofPattern("HH:mm", Locale.getDefault())
        val dayFormatter = DateTimeFormatter.ofPattern("E", Locale.CHINA)
        tvTime.text = "${now.format(timeFormatter)} ${now.format(dayFormatter)}"
    }

}