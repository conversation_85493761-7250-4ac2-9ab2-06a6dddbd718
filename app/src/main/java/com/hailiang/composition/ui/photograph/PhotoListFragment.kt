package com.hailiang.composition.ui.photograph

import android.graphics.Bitmap
import android.graphics.Color
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.SimpleItemAnimator
import com.bumptech.glide.Glide
import com.bumptech.glide.load.MultiTransformation
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.bumptech.glide.request.RequestOptions
import com.hailiang.camera.HLPictureSelector
import com.hailiang.camera.pictureselector.entity.HLLocalMedia
import com.hailiang.camera.pictureselector.interfaces.HLOnResultCallbackListener
import com.hailiang.common.base.BaseFragment
import com.hailiang.common.util.AppToast
import com.hailiang.composition.mediatools.DragItemTouchHelperCallback
import com.hailiang.composition.mediatools.FullyGridLayoutManager
import com.hailiang.composition.mediatools.GridImageAdapter
import com.hailiang.composition.mediatools.MediaImage
import com.hailiang.core.ext.setSingleClickListener
import com.hailiang.hlobs.FileServiceManager
import com.hailiang.hlobs.UploadCallback
import com.hailiang.hlutil.HLog
import com.hailiang.hlutil.HTag
import com.hailiang.hlutil.dp
import com.hailiang.hlutil.dpInt
import com.hailiang.libhttp.BaseRetrofit
import com.hailiang.xxb.composition.R
import com.hailiang.xxb.composition.databinding.FragmentPhotoListBinding
import com.luck.picture.lib.decoration.GridSpacingItemDecoration
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.FileOutputStream

class PhotoListFragment : BaseFragment(R.layout.fragment_photo_list) {

    private val viewModel by activityViewModels<PhotoViewModel>()
    private var _binding: FragmentPhotoListBinding? = null
    private val binding get() = _binding!!
    private var mAdapterTitle: GridImageAdapter? = null
    private var mAdapterComposition: GridImageAdapter? = null
    private val mHandler: Handler = Handler(Looper.getMainLooper())
    private lateinit var dragItemTouchHelperTitle: ItemTouchHelper
    private lateinit var dragItemTouchHelperComposition: ItemTouchHelper


    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        _binding = FragmentPhotoListBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun initView(view: View) {
        initRecycleView()
        initDragItemTouchHelper()
        initClick()

    }

    fun initRecycleView() {
        binding.rlTitle.layoutManager = FullyGridLayoutManager(
            context, 5, GridLayoutManager.VERTICAL, false
        )
        binding.rlComposition.layoutManager = FullyGridLayoutManager(
            context, 5, GridLayoutManager.VERTICAL, false
        )

        (binding.rlTitle.itemAnimator as SimpleItemAnimator).supportsChangeAnimations = false
        (binding.rlComposition.itemAnimator as SimpleItemAnimator).supportsChangeAnimations = false

        binding.rlTitle.addItemDecoration(
            GridSpacingItemDecoration(
                5, 22.dpInt, false
            )
        )

        binding.rlComposition.addItemDecoration(
            GridSpacingItemDecoration(
                5, 22.dpInt, false
            )
        )

        mAdapterTitle = GridImageAdapter(context)
        mAdapterTitle?.selectMax = viewModel.maxTitleNum
        binding.rlTitle.setAdapter(mAdapterTitle)
        mAdapterComposition = GridImageAdapter(context)
        mAdapterComposition?.selectMax = viewModel.maxCompositionNum
        binding.rlComposition.setAdapter(mAdapterComposition)
        mAdapterTitle?.list = (viewModel.titlePictureListLiveData.value ?: listOf()).toMutableList() as ArrayList<MediaImage>
        mAdapterComposition?.list = (viewModel.compositionPictureListLiveData.value ?: listOf()).toMutableList() as ArrayList<MediaImage>
//

        mAdapterTitle?.setOnItemClickListener(object : GridImageAdapter.OnItemClickListener {
            override fun onItemClick(v: View?, position: Int) {
                val dataList = mAdapterTitle?.data ?: return
                if (position < 0 || position >= dataList.size) {
                    return
                }
                val itemData = dataList[position]
                if (itemData.state == MediaImage.STATE_LOADING || itemData.state == MediaImage.STATE_FAILURE) {
                    return
                }
                HLPictureSelector.openPreview(
                    context = requireActivity(),
                    list = dataList.map { it.media },
                    index = position,
                    canDelete = false,
                    isLandscape = true,
                    listener = object : HLOnResultCallbackListener<HLLocalMedia> {
                        override fun onCancel() {
                        }

                        override fun onResult(result: ArrayList<HLLocalMedia>?) {
                        }
                    })
            }

            override fun openPicture() {
                closeOrOpen(false)
                viewModel.typeLiveData.postValue(PhotoViewModel.TYPE_TITLE)
            }

            override fun retry(position: Int) {
                // 直接设置状态并通知适配器更新，然后执行上传
                val mediaImage = mAdapterTitle?.data?.get(position)
                if (mediaImage != null) {
                    mediaImage.state = MediaImage.STATE_LOADING
                    mAdapterTitle?.notifyItemChanged(position)
                    uploadPicture(mediaImage)
                }
            }

            override fun delete(position: Int) {
                changeData(PhotoViewModel.TYPE_TITLE)
            }

        })

        mAdapterComposition?.setOnItemClickListener(object : GridImageAdapter.OnItemClickListener {
            override fun onItemClick(v: View?, position: Int) {
                val dataList = mAdapterComposition?.data ?: return
                if (position < 0 || position >= dataList.size) {
                    return
                }
                val itemData = dataList[position]
                if (itemData.state == MediaImage.STATE_LOADING || itemData.state == MediaImage.STATE_FAILURE) {
                    return
                }
                HLPictureSelector.openPreview(
                    context = requireActivity(),
                    list = dataList.map { it.media },
                    index = position,
                    canDelete = false,
                    isLandscape = false,
                    listener = object : HLOnResultCallbackListener<HLLocalMedia> {
                        override fun onCancel() {
                        }

                        override fun onResult(result: ArrayList<HLLocalMedia>?) {
                        }
                    })
            }

            override fun openPicture() {
                viewModel.typeLiveData.postValue(PhotoViewModel.TYPE_COMPOSITION)
                closeOrOpen(false)
            }

            override fun retry(position: Int) {
                // 直接设置状态并通知适配器更新，然后执行上传
                val mediaImage = mAdapterComposition?.data?.get(position)
                if (mediaImage != null) {
                    mediaImage.state = MediaImage.STATE_LOADING
                    mAdapterComposition?.notifyItemChanged(position)
                    uploadPicture(mediaImage)
                }
            }

            override fun delete(position: Int) {
                changeData(PhotoViewModel.TYPE_COMPOSITION)
            }
        })
    }

    fun initDragItemTouchHelper() {
        dragItemTouchHelperTitle = ItemTouchHelper(
            DragItemTouchHelperCallback(
                mAdapterTitle!!,
                onChange = {
                    changeData(PhotoViewModel.TYPE_TITLE)
                }
            )
        )
        dragItemTouchHelperTitle.attachToRecyclerView(binding.rlTitle)
        dragItemTouchHelperComposition = ItemTouchHelper(
            DragItemTouchHelperCallback(
                mAdapterComposition!!,
                onChange = {
                    changeData(PhotoViewModel.TYPE_COMPOSITION)
                }
            )
        )
        dragItemTouchHelperComposition.attachToRecyclerView(binding.rlComposition)
    }

    fun initClick() {
        binding.viewClose.setOnClickListener {
            closeOrOpen(false)
        }
        binding.clContent.setOnClickListener {
        }
        binding.btnCloseFragment.setOnClickListener {
            closeOrOpen(false)
        }
        binding.btnCorrect.setSingleClickListener {
            viewModel.toCorrect(
                mAdapterTitle?.data!!,
                mAdapterComposition?.data!!,
                requireContext()
            )
        }
        binding.clComposition.setOnClickListener {
            viewModel.typeLiveData.postValue(PhotoViewModel.TYPE_COMPOSITION)
            closeOrOpen(true)
        }
        binding.clTitle.setOnClickListener {
            viewModel.typeLiveData.postValue(PhotoViewModel.TYPE_TITLE)
            closeOrOpen(true)
        }

        binding.ivTitleAdd.setOnClickListener {
            viewModel.typeLiveData.postValue(PhotoViewModel.TYPE_TITLE)
            closeOrOpen(false)
        }

        binding.ivCompositionAdd.setOnClickListener {
            viewModel.typeLiveData.postValue(PhotoViewModel.TYPE_COMPOSITION)
            closeOrOpen(false)
        }
    }

    fun closeOrOpen(isOpen: Boolean) {
        binding.clContent.visibility = if (isOpen) View.VISIBLE else View.GONE
    }

    fun changeData(type: Int) {
        when (type) {
            PhotoViewModel.TYPE_TITLE -> {
                viewModel.titlePictureListLiveData.postValue(mAdapterTitle?.data)
            }

            PhotoViewModel.TYPE_COMPOSITION -> {
                viewModel.compositionPictureListLiveData.postValue(mAdapterComposition?.data)
            }
        }
    }

    fun setPictureResult(iv: ImageView, path: String) {
        val requestOptions = RequestOptions()
            .diskCacheStrategy(DiskCacheStrategy.ALL)
            .transform(
                MultiTransformation<Bitmap?>(
                    CenterCrop(),
                    RoundedCorners(
                        7.dp.toInt()
                    )
                )
            )
        Glide.with(iv.context)
            .load(path)
            .apply(requestOptions)
            .into(iv)
    }

    override fun observeData() {
        viewModel.titlePictureListLiveData.observe(viewLifecycleOwner) {
            binding.llPanel.visibility =
                if (it.isNullOrEmpty() && viewModel.compositionPictureListLiveData.value.isNullOrEmpty()) View.GONE else View.VISIBLE
            binding.rlComposition.visibility =
                if (viewModel.compositionPictureListLiveData.value.isNullOrEmpty()) View.GONE else View.VISIBLE
            binding.rlTitle.visibility = if (it.isNullOrEmpty()) View.GONE else View.VISIBLE
            binding.tvTitleNum.text = if (it.isNullOrEmpty()) "0" else "${it.size}"
            binding.tvTitleNum.visibility = if (it.isNullOrEmpty()) View.INVISIBLE else View.VISIBLE
            it.lastOrNull()?.let { last ->
                setPictureResult(binding.ivTitleResult, last.media.getAvailablePath())
            }
            if (it.isNullOrEmpty()){
                Glide.with(binding.ivTitleResult.context).clear(binding.ivTitleResult)
                if (viewModel.compositionPictureListLiveData.value.isNullOrEmpty()){
                    closeOrOpen(false)
                }
            }
            binding.ivTitleAdd.visibility = if (it.isNullOrEmpty()) View.VISIBLE else View.GONE
            binding.btnCorrect.isEnabled =
                !it.isNullOrEmpty() && !viewModel.compositionPictureListLiveData.value.isNullOrEmpty()
            binding.btnCorrect.setTextColor(if (it.isNullOrEmpty() || viewModel.compositionPictureListLiveData.value.isNullOrEmpty()) 0x80FFFFFF.toInt() else Color.WHITE)

            it?.forEach {
                if (it.url.isNullOrEmpty() || it.media.url.isEmpty()) compressPicture(
                    it
                )
            }
            mAdapterTitle?.list = it as ArrayList<MediaImage>
            mAdapterTitle?.notifyDataSetChanged()
        }

        viewModel.compositionPictureListLiveData.observe(viewLifecycleOwner) {
            binding.llPanel.visibility =
                if (it.isNullOrEmpty() && viewModel.titlePictureListLiveData.value.isNullOrEmpty()) View.GONE else View.VISIBLE
            binding.rlTitle.visibility =
                if (viewModel.titlePictureListLiveData.value.isNullOrEmpty()) View.GONE else View.VISIBLE
            binding.rlComposition.visibility = if (it.isNullOrEmpty()) View.GONE else View.VISIBLE
            binding.tvCompositionNum.text = if (it.isNullOrEmpty()) "0" else "${it.size}"
            binding.tvCompositionNum.visibility =
                if (it.isNullOrEmpty()) View.INVISIBLE else View.VISIBLE
            it.lastOrNull()?.let { last ->
                setPictureResult(binding.ivCompositionResult, last.media.getAvailablePath())
            }
            if (it.isNullOrEmpty()) {
                Glide.with(binding.ivCompositionResult.context).clear(binding.ivCompositionResult)
                if (viewModel.titlePictureListLiveData.value.isNullOrEmpty()) {
                    closeOrOpen(false)
                }
            }
            binding.ivCompositionAdd.visibility =
                if (it.isNullOrEmpty()) View.VISIBLE else View.GONE
            binding.btnCorrect.isEnabled =
                !it.isNullOrEmpty() && !viewModel.titlePictureListLiveData.value.isNullOrEmpty() || (!it.isNullOrEmpty())
            binding.btnCorrect.setTextColor(if (it.isNullOrEmpty() || viewModel.compositionPictureListLiveData.value.isNullOrEmpty()) 0x80FFFFFF.toInt() else Color.WHITE)

            it?.forEach {
                if (it.url.isNullOrEmpty() || it.media.url.isEmpty()) compressPicture(
                    it
                )
            }
            mAdapterComposition?.list = it as ArrayList<MediaImage>
            mAdapterComposition?.notifyDataSetChanged()
        }
        viewModel.stateChangeLiveData.observe(viewLifecycleOwner) {
//            when (it.changeType) {
//                StateChangeEvent.CHANGE_TYPE_ADD -> {
//                    if (it.type == PhotoViewModel.TYPE_TITLE) {
//                        viewModel.titlePictureListLiveData.value?.forEach {
//                            if (it.url.isNullOrEmpty() || it.media.url.isNullOrEmpty())
//                                compressPicture(it)
//                        }
//                        mAdapterTitle?.list = viewModel.titlePictureListLiveData.value as ArrayList<MediaImage>
//                        mAdapterTitle?.notifyDataSetChanged()
//                    } else {
//                        viewModel.compositionPictureListLiveData.value?.forEach {
//                            if (it.url.isNullOrEmpty() || it.media.url.isNullOrEmpty())
//                                compressPicture(it)
//                        }
//                        mAdapterComposition?.list = viewModel.compositionPictureListLiveData.value as ArrayList<MediaImage>
//                        mAdapterComposition?.notifyDataSetChanged()
//                    }
//                }
//            }

        }
        viewModel.typeLiveData.observe(viewLifecycleOwner) {
            binding.viewTypeTitle.visibility =
                if (it == PhotoViewModel.TYPE_TITLE) View.VISIBLE else View.GONE
            binding.viewTypeComposition.visibility =
                if (it == PhotoViewModel.TYPE_COMPOSITION) View.VISIBLE else View.GONE
        }

    }

    //压缩图片
    fun compressPicture(mi: MediaImage) {
        val filePath = mi.media.path
        val fileSize = fileSize(filePath)
        //3兆以上要压缩到2兆
        if (fileSize > 3 * 1024 * 1024) {
            lifecycleScope.launch { compressImage(filePath, 2 * 1024 * 1024, 80, mi) }
        } else if (fileSize > 1.5 * 1024 * 1024) {//压缩到1.5兆
            lifecycleScope.launch { compressImage(filePath, (1.5 * 1024 * 1024).toLong(), 85, mi) }
        } else {//不压缩
            uploadPicture(mi)
        }
    }

    private suspend fun compressImage(
        filePath: String, targetSize: Long, initialQuality: Int, mi: MediaImage,
    ) {
        withContext(Dispatchers.IO) {
            val bitmap =
                Glide.with(requireContext()).asBitmap().load(filePath).apply(RequestOptions())
                    .submit().get()

            var quality = initialQuality
            var outputStream = ByteArrayOutputStream()
            bitmap.compress(Bitmap.CompressFormat.JPEG, quality, outputStream)
            var byteArray = outputStream.toByteArray()

            while (byteArray.size > targetSize && quality > 10) {
                outputStream.reset()
                bitmap.compress(Bitmap.CompressFormat.JPEG, quality, outputStream)
                byteArray = outputStream.toByteArray()
                quality -= 5
            }

            val compressedFile =
                File(viewModel.outputDirectory, "compressed_${System.currentTimeMillis()}.jpg")
            FileOutputStream(compressedFile).use { fos ->
                fos.write(byteArray)
            }

            mi.media.path = compressedFile.path
            uploadPicture(mi)
        }
    }

    fun fileSize(path: String): Long {
        val file = File(path)
        if (!file.exists()) {
            return 0
        }
        return file.length()
    }

    private fun uploadPicture(mi: MediaImage, isRetry: Boolean = false) {
        if (mi.media.path.isNullOrEmpty()) {
            return
        }

        // 在开始上传前设置状态为上传中
        updateMediaImageState(mi, MediaImage.STATE_LOADING)

        val file = File(mi.media.path)
        lifecycleScope.launch {
            withContext(Dispatchers.IO) {
                FileServiceManager.uploadWithPrivate(
                    requireContext(),
                    BaseRetrofit.getInstance().baseUrl().toString(),
                    file,
                    FileServiceManager.createOssObjectName("workcloud", "${file.name}"),
                    object : UploadCallback {
                        override fun onFailure(e: Throwable?) {
                            AppToast.toast("图片上传失败", AppToast.LEVEL_FAIL)
                            // 确保在主线程更新UI状态
                            requireActivity().runOnUiThread {
                                updateMediaImageState(mi, MediaImage.STATE_FAILURE)
                            }
                        }

                        override fun onProgress(currentSize: Long, totalSize: Long) {
                            // 可以在这里更新上传进度，如果需要
                        }

                        override fun onSucceed(url: String) {
                            // 确保在主线程更新UI状态
                            requireActivity().runOnUiThread {
                                updateMediaImageState(mi, MediaImage.STATE_SUCCESS, url)
                            }
                        }
                    })
            }
        }
    }

    private fun updateMediaImageState(mi: MediaImage, newState: Int, newUrl: String? = null) {
        val adapter =
            if (mi.type == PhotoViewModel.TYPE_TITLE) mAdapterTitle else mAdapterComposition

        adapter?.data?.indexOfFirst { it.media.path == mi.media.path }?.takeIf { it >= 0 }
            ?.also { index ->
                val mediaImage = adapter.data!![index] // 此处 data 已经被 ?.indexOfFirst 验证过存在
                mediaImage.state = newState
                if (newUrl != null) {
                    mediaImage.url = newUrl
                    mediaImage.media.url = newUrl
                }
                // 立即更新指定项，而不是整个列表
                adapter.notifyItemChanged(index)
            } ?: HLog.e(HTag.TAG_ERROR, "未找到对应的 MediaImage 对象")
    }



    override fun onDestroyView() {
        super.onDestroyView()
        mHandler.removeCallbacksAndMessages(null)
    }


    override fun initData() {

    }

}
