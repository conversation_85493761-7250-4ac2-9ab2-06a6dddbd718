package com.hailiang.composition.ui.photograph

import android.Manifest
import android.content.ContentValues
import android.content.Context
import android.content.Intent
import android.content.pm.ActivityInfo
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.hardware.display.DisplayManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.os.Handler
import android.os.HandlerThread
import android.os.Looper
import android.provider.MediaStore
import android.util.Log
import android.util.Size
import android.view.View
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.camera.core.AspectRatio
import androidx.camera.core.CameraSelector
import androidx.camera.core.CameraState
import androidx.camera.core.CameraUnavailableException
import androidx.camera.core.ImageAnalysis
import androidx.camera.core.ImageCapture
import androidx.camera.core.ImageCapture.CAPTURE_MODE_MINIMIZE_LATENCY
import androidx.camera.core.ImageCapture.Metadata
import androidx.camera.core.ImageCapture.OnImageSavedCallback
import androidx.camera.core.ImageCapture.OutputFileOptions
import androidx.camera.core.ImageCapture.OutputFileResults
import androidx.camera.core.ImageCaptureException
import androidx.camera.core.Preview
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import androidx.core.content.ContextCompat
import androidx.core.net.toUri
import androidx.lifecycle.lifecycleScope
import com.hailiang.camera.pictureselector.engine.DefaultFileCropEngine
import com.hailiang.camera.pictureselector.entity.HLLocalMedia
import com.hailiang.camera.takephoto.LuminosityAnalyzer
import com.hailiang.camera.ucrop.UCrop
import com.hailiang.common.base.BaseActivity
import com.hailiang.common.util.AppToast
import com.hailiang.composition.mediatools.MediaImage
import com.hailiang.composition.ui.practice.CompositionGuidanceActivity
import com.hailiang.hlutil.ActivityUtil
import com.hailiang.hlutil.HLog
import com.hailiang.hlutil.HTag
import com.hailiang.xxb.composition.R
import com.hailiang.xxb.composition.databinding.ActivityTakePhotoCompositionBinding
import com.robertlevonyan.demo.camerax.utils.ThreadExecutor
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.json.JSONArray
import java.io.File
import java.io.FileOutputStream
import java.util.concurrent.ExecutionException
import java.util.concurrent.locks.ReentrantLock
import kotlin.math.abs
import kotlin.math.max
import kotlin.math.min

class TakePhotoCompositionActivity : BaseActivity() {
    // An instance for display manager to get display change callbacks
    private val displayManager by lazy { getSystemService(DISPLAY_SERVICE) as DisplayManager }

    private val outputDirectory: String by lazy {
        getExternalFilesDir(Environment.DIRECTORY_PICTURES)!!.absolutePath + "/HLCamera/"
    }

    private val cameraDirectory: String by lazy {
        Environment.DIRECTORY_PICTURES + "/HLCamera/"
    }

    private val cameraLock = ReentrantLock()

    private val viewModel by viewModels<PhotoViewModel>()

    private lateinit var binding: ActivityTakePhotoCompositionBinding
    private var preview: Preview? = null
    private var cameraProvider: ProcessCameraProvider? = null
    private var imageCapture: ImageCapture? = null
    private var imageAnalyzer: ImageAnalysis? = null

    private var displayId = -1

    private var lensFacing = CameraSelector.DEFAULT_BACK_CAMERA

    private val photoListFragment: PhotoListFragment by lazy {
        PhotoListFragment()
    }

    private val handler = Handler(Looper.getMainLooper())
    private var displayListener  : DisplayManager.DisplayListener? = null

    private val requestPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        when {
            permissions[Manifest.permission.CAMERA] == true && permissions[Manifest.permission.WRITE_EXTERNAL_STORAGE] == true -> {
                onPermissionGranted()
            }

            else -> {
                Toast.makeText(this, "Permissions not granted", Toast.LENGTH_SHORT).show()
                finish()
            }
        }
    }


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        // Request permissions
        if (allPermissionsGranted()) {
            onPermissionGranted()
        } else {
            requestPermissionLauncher.launch(
                arrayOf(
                    Manifest.permission.CAMERA,
                    Manifest.permission.WRITE_EXTERNAL_STORAGE
                )
            )
        }
    }


    override fun initData() {
        viewModel.outputDirectory =
            getExternalFilesDir(Environment.DIRECTORY_PICTURES)!!.absolutePath + "/HLCamera/"
        viewModel.curWorkId = intent.getLongExtra(WORK_ID, 0)
        viewModel.curWorkStateId = intent.getLongExtra(WORK_STATE_ID, 0)
        viewModel.clearType = intent.getIntExtra(CLEAR_TYPE, -1)
        if (viewModel.curWorkId > 0) {
            viewModel.getCompositionDetail()
        }
    }

    private fun allPermissionsGranted() = REQUIRED_PERMISSIONS.all {
        ContextCompat.checkSelfPermission(baseContext, it) == PackageManager.PERMISSION_GRANTED
    }

    override fun observeData() {
        viewModel.typeLiveData.observe(this) {
            when (it) {
                PhotoViewModel.TYPE_TITLE -> {
                    binding.groupTwoPart.visibility = View.INVISIBLE
                    binding.groupThreePart.visibility = View.INVISIBLE
                    requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
                    binding.tvHint.text = "请确保图片朝上"
                    binding.radioGroupColumns.visibility = View.GONE
                    binding.tv0.text = "拍题目"
                }

                PhotoViewModel.TYPE_COMPOSITION -> {
                    binding.radioGroupColumns.visibility = View.VISIBLE
                    binding.tv0.text = "拍作文"
                    updateUiBySliceCount(viewModel.sliceCountLiveData.value ?: 2)
                }
            }
            binding.tv0.visibility = View.VISIBLE
            handler.removeCallbacksAndMessages(null)
            handler.postDelayed({
                binding.tv0.visibility = View.INVISIBLE
            }, 3000)
        }
        viewModel.typeLiveData.observe(this) {
            if (it == PhotoViewModel.TYPE_TITLE && binding.radioGroupType.checkedRadioButtonId != R.id.rbTitle){
                binding.rbTitle.performClick()
            }
            if (it == PhotoViewModel.TYPE_COMPOSITION && binding.radioGroupType.checkedRadioButtonId != R.id.rbComposition){
                binding.rbComposition.performClick()
            }

        }
        viewModel.sliceCountLiveData.observe(this) {
            if (viewModel.typeLiveData.value == PhotoViewModel.TYPE_COMPOSITION) {
                updateUiBySliceCount(viewModel.sliceCountLiveData.value ?: 2)
            }
        }
        viewModel.addCompositionResultLiveData.observe(this) {
            if (it) {
//                CompositionGuidanceActivity.start(
//                    this@TakePhotoCompositionActivity,
//                    viewModel.curWorkId,
//                    viewModel.curWorkStateId
//                )
//                setResult(RESULT_OK)
//                finish()
                val resultIntent = Intent().apply {
                    putExtra(WORK_ID, viewModel.curWorkId)
                    putExtra(WORK_STATE_ID, viewModel.curWorkStateId)
                }
                setResult(RESULT_OK, resultIntent)
                finish()
            }
        }
    }

    fun updateUiBySliceCount(sliceCount: Int) {
        when (sliceCount) {
            1 -> {
                binding.tvHint.text = "请确保图片朝上"
                binding.groupTwoPart.visibility = View.INVISIBLE
                binding.groupThreePart.visibility = View.INVISIBLE
                requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
            }

            2 -> {
                binding.groupTwoPart.visibility = View.VISIBLE
                binding.groupThreePart.visibility = View.INVISIBLE
                binding.tvHint.text = "请确保图片朝上，按照1、2顺序拍摄"
                requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
            }

            3 -> {
                binding.groupTwoPart.visibility = View.INVISIBLE
                binding.groupThreePart.visibility = View.VISIBLE
                binding.tvHint.text = "请确保图片朝上，按照1、2、3顺序拍摄"
                requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
            }
        }
    }


    override fun initView() {
        binding = ActivityTakePhotoCompositionBinding.inflate(layoutInflater)
        setContentView(binding.root)
        displayListener = object : DisplayManager.DisplayListener {
            override fun onDisplayAdded(displayId: Int) = Unit
            override fun onDisplayRemoved(displayId: Int) = Unit

            override fun onDisplayChanged(displayId: Int) = binding.root.let { view ->
                if (displayId == <EMAIL>) {
                    val display = view.display
                    if (display != null) {
                        val rotation = display.rotation
                        preview?.targetRotation = rotation
                        imageCapture?.targetRotation = rotation
                        imageAnalyzer?.targetRotation = rotation
                    } else {
                        Log.e(TAG, "Display is null in onDisplayChanged")
                    }
                }
            }
        }
        displayManager.registerDisplayListener(displayListener, null)

        binding.run {
            viewFinder.addOnAttachStateChangeListener(object :
                View.OnAttachStateChangeListener {
                override fun onViewDetachedFromWindow(v: View) {}

                override fun onViewAttachedToWindow(v: View) =
                    displayManager.unregisterDisplayListener(displayListener)
            })
            btnTakePicture.setOnClickListener {
                takePicture()
                btnTakePicture.isEnabled = false
            }
            viewFinder.implementationMode = PreviewView.ImplementationMode.COMPATIBLE
            btnClose.setOnClickListener {
                finish()
            }

            radioGroupColumns.setOnCheckedChangeListener { group, i ->
                when (i) {
                    R.id.radioButtonSingle -> {
                        viewModel.sliceCountLiveData.postValue(1)
                    }

                    R.id.radioButtonDouble -> {
                        viewModel.compositionPictureListLiveData.value?.let {
                            if ((it.size + 2) > viewModel.maxCompositionNum) {
                                AppToast.toast("最多上传5张图片")
                            }
                        }
                        viewModel.sliceCountLiveData.postValue(2)
                    }

                    R.id.radioButtonTriple -> {
                        viewModel.compositionPictureListLiveData.value?.let {
                            if ((it.size + 3) > viewModel.maxCompositionNum) {
                                AppToast.toast("最多上传5张图片")
                            }
                        }
                        viewModel.sliceCountLiveData.postValue(3)
                    }
                }
            }
            radioGroupType.setOnCheckedChangeListener { group, i ->
                when (i) {
                    R.id.rbComposition -> {
                        viewModel.typeLiveData.postValue(PhotoViewModel.TYPE_COMPOSITION)
                    }

                    R.id.rbTitle -> {
                        viewModel.typeLiveData.postValue(PhotoViewModel.TYPE_TITLE)
                    }
                }
            }
        }
        if (supportFragmentManager.findFragmentByTag("PhotoListFragment") == null) {
            supportFragmentManager.beginTransaction()
                .add(R.id.fl_fragment, photoListFragment, "PhotoListFragment")
                .commit()
        }
    }


    @Suppress("NON_EXHAUSTIVE_WHEN")
    private fun takePicture() = lifecycleScope.launch(Dispatchers.Main) {
        captureImage()
    }


    private fun captureImage() {
        if (cameraProvider == null || imageCapture == null) {
            Log.w(TAG, "相机未初始化，跳过拍照")
            binding.btnTakePicture.isEnabled = true
            return
        }
        if (viewModel.titlePictureListLiveData.value?.size == viewModel.maxTitleNum && viewModel.typeLiveData.value == PhotoViewModel.TYPE_TITLE) {
            AppToast.toast("题目最多拍摄2张图片")
            binding.btnTakePicture.isEnabled = true
            return
        }
        if (viewModel.typeLiveData.value == PhotoViewModel.TYPE_COMPOSITION) {
            val currentSize = viewModel.compositionPictureListLiveData.value?.size ?: 0
            val sliceCount = viewModel.sliceCountLiveData.value ?: 1
            if (currentSize + sliceCount > viewModel.maxCompositionNum) {
                AppToast.toast("作文最多拍摄5张图片")
                binding.btnTakePicture.isEnabled = true
                return
            }
        }

        val localImageCapture =
            imageCapture ?: throw IllegalStateException("Camera initialization failed.")

        // Setup image capture metadata
        val metadata = Metadata().apply {
            // Mirror image when using the front camera
            isReversedHorizontal = lensFacing == CameraSelector.DEFAULT_FRONT_CAMERA
        }
        // Set target resolution
        val targetResolution = Size(1080, 1920) // Example resolution

        // Options for the output image file
        val outputOptions = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            val contentValues = ContentValues().apply {
                put(MediaStore.MediaColumns.DISPLAY_NAME, System.currentTimeMillis())
                put(MediaStore.MediaColumns.MIME_TYPE, "image/jpeg")
                put(MediaStore.MediaColumns.RELATIVE_PATH, cameraDirectory)
                put(
                    MediaStore.Images.Media.RESOLUTION,
                    "${targetResolution.width}x${targetResolution.height}"
                )
            }

            // Create the output uri
            val contentUri =
                MediaStore.Images.Media.getContentUri(MediaStore.VOLUME_EXTERNAL_PRIMARY)

            OutputFileOptions.Builder(contentResolver, contentUri, contentValues)
        } else {
            File(cameraDirectory).mkdirs()
            val file = File(cameraDirectory, "${System.currentTimeMillis()}.jpg")

            OutputFileOptions.Builder(file)
        }.setMetadata(metadata).build()

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            localImageCapture.takePicture(
                outputOptions, // the options needed for the final image
                mainExecutor, // the executor, on which the task will run
                object : OnImageSavedCallback { // the callback, about the result of capture process
                    override fun onImageSaved(outputFileResults: OutputFileResults) {
                        binding.btnTakePicture.isEnabled = true
                        // This function is called if capture is successfully completed
                        outputFileResults.savedUri
                            ?.let { uri ->
                                Log.d(TAG, "Photo saved in $uri")
                                handleImageSlicing(uri)
                            }
                    }

                    override fun onError(exception: ImageCaptureException) {
                        // This function is called if there is an error during capture process
                        val msg = "拍照失败: ${exception.message}"
                        if (binding.btnTakePicture.isEnabled == true) {
                            AppToast.toast(msg, AppToast.LEVEL_FAIL)
                        }
                        binding.btnTakePicture.isEnabled = true
                        Log.e(TAG, msg)
                        exception.printStackTrace()
                    }
                }
            )
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == UCrop.REQUEST_CROP && resultCode == RESULT_OK) {
            val list = ArrayList<HLLocalMedia>()
            data?.extras?.let {
                val output = it.getString("output")
                if (output != null) {
                    try {
                        val jsonArray = JSONArray(output)

                        for (i in 0 until jsonArray.length()) {
                            val jsonObject = jsonArray.getJSONObject(i)

                            val localMedia = HLLocalMedia(
                                jsonObject.getInt("imageWidth"),
                                jsonObject.getInt("imageHeight"),
                                0,
                                jsonObject.getString("outPutPath"),
                                "image/jpeg",
                                ""
                            )
                            list.add(localMedia)
                        }
                        filterHLLocalMedia(list)
                    } catch (e: Exception) {
                        Log.e(TAG, "TitleOcrFailed to parse JSON", e)
                        AppToast.toast("解析图片信息失败", AppToast.LEVEL_FAIL)
                    }
                } else {
                    val outUri = UCrop.getOutput(data)
                    val outWidth = UCrop.getOutputImageWidth(data)
                    val outHeight = UCrop.getOutputImageHeight(data)
                    val localMedia = HLLocalMedia(
                        outWidth,
                        outHeight,
                        0,
                        outUri.toString(),
                        "image/jpeg",
                        "",
                    )

                    list.add(localMedia)
                    filterHLLocalMedia(list)
                }
            }
        }
    }

    private fun filterHLLocalMedia(list: ArrayList<HLLocalMedia>) {
        var titleList = viewModel.titlePictureListLiveData.value
        var compositionList = viewModel.compositionPictureListLiveData.value
        if (viewModel.typeLiveData.value == PhotoViewModel.TYPE_TITLE) {
            if (titleList == null) {
                titleList = mutableListOf()
            }
            titleList.addAll(list.map { it ->
                MediaImage(type = PhotoViewModel.TYPE_TITLE, media = it)
            })
            viewModel.titlePictureListLiveData.value = titleList
        } else {
            if (compositionList == null) {
                compositionList = mutableListOf()
            }
            compositionList.addAll(list.map { it ->
                MediaImage(type = PhotoViewModel.TYPE_COMPOSITION, media = it)
            })
            viewModel.compositionPictureListLiveData.value = compositionList
        }
//        viewModel.stateChangeLiveData.postValue(StateChangeEvent(
//            viewModel.typeLiveData.value ?: PhotoViewModel.TYPE_TITLE,
//            StateChangeEvent.CHANGE_TYPE_ADD,
//            list
//        ))
        if (viewModel.typeLiveData.value == PhotoViewModel.TYPE_TITLE){
            viewModel.typeLiveData.postValue(PhotoViewModel.TYPE_COMPOSITION)
        }
    }

    private fun handleImageSlicing(uri: Uri) {
        val bitmap = uriToBitmap(uri) ?: return
        val time = System.currentTimeMillis()
        // 去裁剪
        val file = File(outputDirectory, "${time}-crop.jpg")
        // 确保目录存在
        if (file.parentFile?.exists() == false) {
            file.parentFile?.mkdirs()
        }
        val listStr = ArrayList<String>()
        if (viewModel.typeLiveData.value == PhotoViewModel.TYPE_COMPOSITION) {
            when (viewModel.sliceCountLiveData.value) {
                2 -> {
                    // Slice into two parts
                    val (leftBitmap, rightBitmap) = splitBitmapHorizontally(bitmap, 2)
                    saveBitmap(leftBitmap, "${time}_A.jpg")
                    saveBitmap(rightBitmap, "${time}_B.jpg")
                    val list = ArrayList<HLLocalMedia>()
                    list.add(getLocalMedia(leftBitmap, "${time}_A.jpg"))
                    list.add(getLocalMedia(rightBitmap, "${time}_B.jpg"))

                    listStr.add(File(outputDirectory, "${time}_A.jpg").path)
                    listStr.add(File(outputDirectory, "${time}_B.jpg").path)
                }

                3 -> {
                    // Slice into three parts
                    val (leftBitmap, middleBitmap, rightBitmap) = splitBitmapHorizontally(
                        bitmap,
                        3
                    )
                    saveBitmap(leftBitmap, "${time}_A.jpg")
                    saveBitmap(middleBitmap, "${time}_B.jpg")
                    saveBitmap(rightBitmap, "${time}_C.jpg")

                    listStr.add(File(outputDirectory, "${time}_A.jpg").path)
                    listStr.add(File(outputDirectory, "${time}_B.jpg").path)
                    listStr.add(File(outputDirectory, "${time}_C.jpg").path)

                }

                else -> {
                    saveBitmap(bitmap, "${time}.jpg")
                    val list = ArrayList<HLLocalMedia>()
                    list.add(getLocalMedia(bitmap, "${time}.jpg"))
                    listStr.add(File(outputDirectory, "${time}.jpg").path)

                }
            }

            DefaultFileCropEngine().onStartCrop(
                this@TakePhotoCompositionActivity,
                uri,
                file.toUri(),
                listStr,
                false,
                viewModel.typeLiveData.value == PhotoViewModel.TYPE_COMPOSITION,
                UCrop.REQUEST_CROP
            )
        } else {
            saveBitmap(bitmap, "${time}.jpg")
            // 去裁剪
            val file = File(outputDirectory, "${time}-crop.jpg")
            // 确保目录存在
            if (file.parentFile?.exists() == false) {
                file.parentFile?.mkdirs()
            }
            val list: ArrayList<String> = ArrayList()
            list.add(uri.toString())
            DefaultFileCropEngine().onStartCrop(
                this@TakePhotoCompositionActivity,
                uri,
                file.toUri(),
                list,
                false,
                viewModel.typeLiveData.value == PhotoViewModel.TYPE_COMPOSITION,
                UCrop.REQUEST_CROP
            )
        }
    }

    fun getLocalMedia(bitmap: Bitmap, fileName: String): HLLocalMedia {
        val localMedia = HLLocalMedia(
            bitmap.width,
            bitmap.height,
            0,
            File(outputDirectory, fileName).path.toString(),
            "image/jpeg",
            ""
        )
        return localMedia
    }

    private fun uriToBitmap(uri: Uri): Bitmap? {
        return try {
            val inputStream = contentResolver.openInputStream(uri)
            BitmapFactory.decodeStream(inputStream)
        } catch (e: Exception) {
            Log.e(TAG, "TitleOcrFailed to convert URI to Bitmap", e)
            null
        }
    }


    private fun splitBitmapHorizontally(bitmap: Bitmap, parts: Int): List<Bitmap> {
        val width = bitmap.width
        val height = bitmap.height
        val bitmaps = mutableListOf<Bitmap>()
        if (width > height) {
            val partWidth = width / parts
            for (i in 0 until parts) {
                val x = i * partWidth
                val partBitmap = Bitmap.createBitmap(bitmap, x, 0, partWidth, height)
                bitmaps.add(partBitmap)
            }
        } else {
            val partHeight = height / parts

            for (i in 0 until parts) {
                val y = i * partHeight
                val partBitmap = Bitmap.createBitmap(bitmap, 0, y, width, partHeight)
                bitmaps.add(partBitmap)
            }
        }

        return bitmaps
    }

    private fun saveBitmap(bitmap: Bitmap, fileName: String) {
        val file = File(outputDirectory, fileName)
        // 确保目录存在
        if (file.parentFile?.exists() == false) {
            file.parentFile?.mkdirs()
        }

        try {
            val outputStream = FileOutputStream(file)
            bitmap.compress(Bitmap.CompressFormat.JPEG, 85, outputStream)
            outputStream.flush()
            outputStream.close()
        } catch (_: Exception) {
            AppToast.toast("保存图片失败",  AppToast.LEVEL_FAIL)
        }
    }

    private fun onPermissionGranted() {

        // Each time apps is coming to foreground the need permission check is being processed
        binding.viewFinder.let { vf ->
            vf.addOnAttachStateChangeListener(object : View.OnAttachStateChangeListener {
                override fun onViewDetachedFromWindow(v: View) {
                    // Do nothing or unregister listeners if needed
                }

                override fun onViewAttachedToWindow(v: View) {
                    if (vf.display == null) {
                        Log.e(TAG, "Display is null, cannot get display ID, posting delayed task")
                        vf.post {
                            if (vf.display == null) {
                                Log.e(
                                    TAG,
                                    "Display is still null after post, cannot get display ID"
                                )
                                return@post
                            }
                            // Setting current display ID
                            displayId = vf.display.displayId
                            Log.i(TAG, "Display ID is $displayId")
                            startCamera()
                        }
                    } else {
                        // Setting current display ID
                        displayId = vf.display.displayId
                        Log.i(TAG, "Display ID is $displayId")
                        startCamera()
                    }
                }
            })
        }
    }


    /**
     * Unbinds all the lifecycles from CameraX, then creates new with new parameters
     * */
    private fun startCamera() {
        HLog.d(
            TAG,
            "尝试启动相机，当前状态: provider=${cameraProvider != null}, displayId=$displayId"
        )
        if (!cameraLock.tryLock()) {
            HLog.w(TAG, "相机操作冲突，跳过")
            return
        }

        try {
            // This is the CameraX PreviewView where the camera will be rendered
            val viewFinder = binding.viewFinder

            val cameraProviderFuture = ProcessCameraProvider.getInstance(this)
            cameraProviderFuture.addListener({
                try {
                    cameraProvider = cameraProviderFuture.get()
                } catch (_: InterruptedException) {
                    AppToast.toast("相机启动失败", AppToast.LEVEL_FAIL)
                    return@addListener
                } catch (_: ExecutionException) {
                    AppToast.toast("相机启动失败", AppToast.LEVEL_FAIL)
                    return@addListener
                }

                // The ratio for the output image and preview
                val aspectRatio = aspectRatio(viewFinder.width, viewFinder.height)
                if (viewFinder.display ==null){
                    AppToast.toast("获取屏幕旋转角度失败", AppToast.LEVEL_FAIL)
                    return@addListener
                }
                // The display rotation
                val rotation = viewFinder.display.rotation

                val localCameraProvider = cameraProvider
                    ?: throw IllegalStateException("Camera initialization failed.")

                // The Configuration of camera preview
                preview = Preview.Builder()
                    .setTargetAspectRatio(aspectRatio) // set the camera aspect ratio
                    .setTargetRotation(rotation) // set the camera rotation
                    .build()

                // The Configuration of image capture
                imageCapture = ImageCapture.Builder()
                    .setCaptureMode(CAPTURE_MODE_MINIMIZE_LATENCY) // setting to have pictures with highest quality possible (may be slow)
                    .setTargetAspectRatio(aspectRatio) // set the capture aspect ratio
                    .setTargetRotation(rotation) // set the capture rotation
                    .build()


                // The Configuration of image analyzing
                imageAnalyzer = ImageAnalysis.Builder()
                    .setTargetAspectRatio(aspectRatio) // set the analyzer aspect ratio
                    .setTargetRotation(rotation) // set the analyzer rotation
                    .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST) // in our analysis, we care about the latest image
                    .build()
                    .also { setLuminosityAnalyzer(it) }
                // Unbind the use-cases before rebinding them
                localCameraProvider.unbindAll()
                // Bind all use cases to the camera with lifecycle
                bindToLifecycle(localCameraProvider, viewFinder)
            }, ContextCompat.getMainExecutor(this))
        } finally {
            cameraLock.unlock()
        }
    }

    private var analyzerThread: HandlerThread? = null
    private fun setLuminosityAnalyzer(imageAnalysis: ImageAnalysis) {
        analyzerThread?.quitSafely()

        analyzerThread = HandlerThread("LuminosityAnalysis").apply {
            start()
        }

        imageAnalysis.setAnalyzer(
            ThreadExecutor(Handler(analyzerThread!!.looper)),
            LuminosityAnalyzer()
        )
    }

    private fun bindToLifecycle(
        localCameraProvider: ProcessCameraProvider,
        viewFinder: PreviewView
    ) {
        try {
            localCameraProvider.bindToLifecycle(
                this,
                lensFacing,
                preview,
                imageCapture,
                imageAnalyzer
            ).also { camera ->
                // 添加相机状态监听（CameraX 1.3+ 支持）
                camera.cameraInfo.cameraState.observe(this) { state ->
                    if (state.error != null) {
                        Log.e(TAG, "相机发生错误: ${state.error}")
                        when (state.error?.code) {
                            CameraState.ERROR_OTHER_RECOVERABLE_ERROR -> {
//                                handleRecoverableError()
                            }

                            else -> {
                                releaseCamera()
                                handler.removeCallbacksAndMessages(null)
                                handler.postDelayed({ startCamera() }, 2000)
                            }

                        }
                    }
//                    if (state.type == CameraState.Type.OPEN) {
//                        if (viewModel.typeLiveData.value == PhotoViewModel.TYPE_COMPOSITION && viewModel.sliceCountLiveData.value == 1) {
//                            requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
//                        }
//                    }
                }
//                cameraControl = camera.cameraControl // 获取控制对象
//                setPreviewZoom()
            }
            preview?.setSurfaceProvider(viewFinder.surfaceProvider)
        } catch (e: CameraUnavailableException) {
            Log.e(TAG, "摄像头不可用: ${e.message}")
            Toast.makeText(this, "摄像头被占用，2秒后重试", Toast.LENGTH_SHORT).show()
            handler.removeCallbacksAndMessages(null)
            handler.postDelayed({ startCamera() }, 2000)
        } catch (e: Exception) {
            Log.e(TAG, "绑定失败: ${e.message}")
        }
    }

    override fun onPause() {
        super.onPause()
    }

    private fun releaseCamera() {
        Log.d(TAG, "释放相机资源...")
        try {
            cameraProvider?.unbindAll()
            imageCapture = null
            preview = null
            imageAnalyzer = null
            
            // 确保线程资源正确清理
            analyzerThread?.quitSafely()
            analyzerThread?.join(1000) // 等待线程安全退出
            analyzerThread = null
        } catch (e: Exception) {
            Log.e(TAG, "释放资源异常: ${e.message}")
        }
    }


    override fun onDestroy() {
        super.onDestroy()
        handler.removeCallbacksAndMessages(null)
        releaseCamera()
        
        // 安全注销displayListener，避免空指针
        displayListener?.let { listener ->
            displayManager.unregisterDisplayListener(listener)
        }
        displayListener = null
    }


    /**
     *  Detecting the most suitable aspect ratio for current dimensions
     *
     *  @param width - preview width
     *  @param height - preview height
     *  @return suitable aspect ratio
     */
    private fun aspectRatio(width: Int, height: Int): Int {
        val previewRatio = max(width, height).toDouble() / min(width, height)
        if (abs(previewRatio - RATIO_4_3_VALUE) <= abs(previewRatio - RATIO_16_9_VALUE)) {
            return AspectRatio.RATIO_4_3
        }
        return AspectRatio.RATIO_16_9
    }

    companion object {
        private const val RATIO_4_3_VALUE = 4.0 / 3.0 // aspect ratio 4x3
        private const val RATIO_16_9_VALUE = 16.0 / 9.0 // aspect ratio 16x9

        private val REQUIRED_PERMISSIONS =
            arrayOf(Manifest.permission.CAMERA, Manifest.permission.WRITE_EXTERNAL_STORAGE)
        private const val WORK_ID = "workId"
        private const val WORK_STATE_ID = "workStateId"
        private const val CLEAR_TYPE = "clearType" //0：题目失败，清空题目 1：作文失败，清空作文 2：清理所有

        fun actionStart(context: Context, workId: Long, workStateId: Long, clearType: Int? = -1) {
            val intent = createIntent(
                context = context,
                workId = workId,
                workStateId = workStateId,
                clearType = clearType
            )
            ActivityUtil.startActivity(context, intent)
        }

        fun createIntent(
            context: Context,
            workId: Long,
            workStateId: Long,
            clearType: Int? = -1,
        ): Intent {
            val intent = Intent(context, TakePhotoCompositionActivity::class.java)
            intent.putExtra(WORK_ID, workId)
            intent.putExtra(WORK_STATE_ID, workStateId)
            intent.putExtra(CLEAR_TYPE, clearType)
            return intent
        }
    }
}